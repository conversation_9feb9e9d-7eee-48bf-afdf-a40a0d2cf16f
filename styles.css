/* 全局样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  /* font-family: 'Arial', sans-serif; */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', <PERSON>l, sans-serif;
}

body {
  background-color: #f0f2f5;
  color: #333;
  overflow: hidden;
}

.container {
  width: 500px;
  height: 600px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

header {
  background-color: #00a884;
  color: white;
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.config-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.config-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 主内容区域 */
.main-content {
  display: flex;
  height: calc(100% - 50px);
  position: relative;
}

/* 分组部分 */
.groups-section {
  width: 25%;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 550px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.section-header h2 {
  font-size: 1rem;
  font-weight: 500;
}

.action-btn {
  background-color: #00a884;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
}

.groups-container {
  padding: 10px;
  overflow-y: auto;
  flex: 1;
}

.group-item {
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 6px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-item:hover {
  background-color: #f0f0f0;
}

.group-item.active {
  background-color: #e7f7ef;
  border-left: 3px solid #00a884;
}

.delete-btn {
  color: #666;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  visibility: hidden;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.group-item:hover .delete-btn {
  visibility: visible;
}

/* 快捷回复部分 */
.replies-section {
  width: 75%;
  display: flex;
  flex-direction: column;
  height: 550px;
}

.replies-container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  flex: 1;
}

.reply-item {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  position: relative;
  min-height: 80px; /* 改为最小高度，而不是固定高度 */
  display: flex;
  align-items: center;
  overflow: hidden;
}

.reply-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.reply-image-container {
  width: 60px;
  height: 60px;
  min-width: 60px; /* 确保不会被压缩 */
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.reply-image {
  max-width: 100%;
  height: 100%;
  object-fit: cover;
}





.reply-content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-right: 10px;
  justify-content: center;
}

.reply-title {
  font-weight: bold;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
}

.reply-content {
  font-size: 0.9rem;
  overflow: hidden;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.reply-item .delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  visibility: hidden;
  color: #ff5252;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 13px;
  z-index: 2;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reply-item:hover .delete-btn {
  visibility: visible;
}

/* 菜单按钮样式 */
.menu-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  visibility: hidden;
  color: #888;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  z-index: 2;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* font-family: 'Arial', sans-serif; */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', Arial, sans-serif;
  letter-spacing: 1px;
  opacity: 0.8;
  transition: all 0.2s ease;
  border-radius: 50%;
}

.menu-btn::after {
  content: '\22EE'; /* 垂直省略号 Unicode */
  font-size: 18px;
  line-height: 1;
}

.menu-btn:hover {
  color: #333;
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

.reply-item:hover .menu-btn,
.group-item:hover .menu-btn {
  visibility: visible;
}

/* 菜单样式 */
.context-menu {
  position: absolute;
  background: white;
  border-radius: 6px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  display: none;
  border: 1px solid rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.context-menu.active {
  display: block;
}

.context-menu-item {
  padding: 8px 14px;
  cursor: pointer;
  font-size: 13px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  color: #333;
  transition: all 0.1s ease;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item:active {
  background-color: #e8e8e8;
}

/* 弹窗样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  background-color: #fefefe;
  padding: 20px;
  border: none;
  width: 85%;
  max-width: 400px;
  max-height: 85vh;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 101;
  overflow-y: auto;
  margin: 0;
  overflow: hidden;
}

.modal-content h2 {
  margin-bottom: 15px;
  color: #00a884;
  text-align: center;
  font-size: 1.3rem;
}

.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 10px;
}

.close:hover,
.close:focus {
  color: #555;
  text-decoration: none;
}

.form-group {
  margin-bottom: 12px;
}

/* 特别处理图片上传表单组 */
.image-upload-group {
  margin-bottom: 0; /* 移除底部边距，因为图片预览区域已经有上边距 */
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #555;
}

.form-group input[type="text"],
.form-group textarea {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border 0.3s;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus {
  border-color: #00a884;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.2);
}

.form-group textarea {
  height: 80px;
  resize: none; /* 禁止调整大小 */
}

.form-group input[type="file"] {
  width: 100%;
  padding: 8px 0;
  cursor: pointer;
}

.btn {
  background-color: #00a884;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  width: 100%;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: #008f70;
}

/* 多图片预览容器 */
.images-preview {
  margin-top: 10px;
  max-width: 100%;
  min-height: 100px;
  background-color: #f8f8f8;
  border-radius: 6px;
  border: 2px dashed #ddd;
  box-sizing: border-box;
  padding: 8px;
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  overflow-x: auto;
  overflow-y: hidden;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.images-preview::-webkit-scrollbar {
  display: none;
}

.images-preview:empty {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.images-preview:empty::before {
  content: "未选择图片";
  color: #999;
  font-size: 14px;
}

/* 单个图片预览项 */
.image-preview-item {
  position: relative;
  width: 80px;
  height: 80px;
  min-width: 80px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #ddd;
  background-color: white;
  flex-shrink: 0;
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 图片预览项悬停效果 */
.image-preview-item:hover {
  opacity: 0.8;
  cursor: pointer;
}

.image-preview-item:hover::after {
  content: "双击删除";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  pointer-events: none;
}

/* 兼容旧的单图片预览 */
.image-preview {
  margin-top: 10px;
  max-width: 100%;
  height: 100px;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 2px dashed #ddd;
  box-sizing: border-box;
}

.image-preview:empty {
  min-height: 100px;
}

.image-preview img {
  max-width: 90%;
  max-height: 84px;
  border-radius: 4px;
  object-fit: contain;
}

/* 为添加快捷回复模态框特别优化 */
#reply-modal-content .form-group {
  margin-bottom: 10px;
}

#reply-modal-content .form-group:last-of-type {
  margin-bottom: 15px; /* 增加最后一个表单组的底部边距 */
}

/* 自定义文件上传按钮样式 */
.custom-file-upload {
  position: relative;
  overflow: hidden;
  display: inline-block;
  width: 100%;
}

.custom-file-upload input[type="file"] {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.file-upload-btn {
  display: block;
  background-color: #f5f5f5;
  color: #555;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 12px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  width: 100%;
}

.file-upload-btn:hover {
  background-color: #e9e9e9;
  border-color: #ccc;
}

.file-name-display {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.reply-image-container:empty {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23cccccc" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 24px;
}

#edit-reply-modal-content,
#edit-group-modal-content {
  margin: 0;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

#edit-reply-modal-content .form-group {
  margin-bottom: 10px;
}

#edit-reply-modal-content .form-group:last-of-type {
  margin-bottom: 15px;
}

/* 空状态提示样式 */
.empty-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  color: #8e8e8e;
  font-size: 13px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  margin: 5px 0;
  min-height: 60px;
} 