# QuickReply 拖拽排序功能说明

## 新增功能概述

为QuickReply扩展添加了完整的拖拽排序功能，支持对分组和快捷回复进行拖拽重新排序。

## 主要特性

### 1. 拖拽排序支持
- **分组排序**: 可以拖拽分组项目重新排序
- **快捷回复排序**: 可以拖拽快捷回复项目重新排序
- **视觉反馈**: 拖拽时提供清晰的视觉反馈和占位符
- **平滑动画**: 包含过渡动画和视觉效果

### 2. 数据持久化
- **数据库升级**: IndexedDB版本升级到v2，支持sortOrder字段
- **自动迁移**: 现有数据自动添加排序字段
- **实时保存**: 拖拽完成后立即保存到数据库

### 3. 用户界面改进
- **拖拽手柄**: 每个项目添加了拖拽手柄图标
- **拖拽状态**: 拖拽时的视觉状态反馈
- **占位符**: 拖拽过程中显示插入位置

## 技术实现

### 数据结构更新
```javascript
// 分组数据结构
{
  id: string,
  name: string,
  sortOrder: number  // 新增排序字段
}

// 快捷回复数据结构
{
  id: string,
  groupId: string,
  title: string,
  content: string,
  images: array,
  sortOrder: number  // 新增排序字段
}
```

### 核心功能
1. **拖拽事件处理**: 鼠标按下、移动、释放事件
2. **位置计算**: 实时计算拖拽位置和插入点
3. **数据更新**: 重新排序数据并保存到数据库
4. **UI更新**: 重新渲染界面反映新的排序

### 性能优化
- 使用 `requestAnimationFrame` 优化拖拽动画性能
- 批量数据库操作减少I/O次数
- 事件委托减少内存占用

## 使用方法

### 拖拽分组
1. 将鼠标悬停在分组项目上
2. 点击并按住分组项目的任意位置（避开删除按钮）
3. 稍微移动鼠标开始拖拽
4. 拖拽到目标位置
5. 释放鼠标完成排序

### 拖拽快捷回复
1. 将鼠标悬停在快捷回复项目上
2. 点击并按住快捷回复项目的任意位置（避开菜单按钮）
3. 稍微移动鼠标开始拖拽
4. 拖拽到目标位置
5. 释放鼠标完成排序

### 智能拖拽检测
- 系统会自动检测您的意图：短时间点击会执行正常的选择/发送操作
- 按住并移动鼠标超过5像素距离会启动拖拽模式
- 拖拽过程中会显示蓝色虚线占位符指示插入位置

## 兼容性

### 数据兼容性
- 完全向后兼容现有数据
- 自动为旧数据添加排序字段
- 导入导出功能支持新的数据格式

### 浏览器兼容性
- 支持所有现代浏览器
- 使用标准的拖拽API和事件
- 响应式设计适配不同屏幕尺寸

## 样式特性

### 拖拽手柄
- 六个点的图标设计
- 悬停时高亮显示
- 拖拽时光标变化

### 拖拽状态
- 拖拽元素轻微旋转和阴影
- 占位符显示插入位置
- 全局拖拽状态管理

### 动画效果
- 平滑的过渡动画
- 拖拽时的视觉反馈
- 释放时的回弹效果

## 错误处理

- 拖拽冲突检测
- 数据库操作错误处理
- 边界条件检查
- 用户友好的错误提示

## 配置选项

所有拖拽相关的样式和行为都可以通过CSS和JavaScript进行自定义调整。
