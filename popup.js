// 全局变量
let currentGroupId = null;
let groups = [];
let replies = {};
let currentEditingItemId = null; // 当前正在编辑的项目ID
let isEditingGroup = false; // 是否正在编辑分组
let db; // IndexedDB数据库实例
let imageFiles = []; // 当前选择的图片文件数组
let editImageFiles = []; // 当前编辑的图片文件数组
let currentEditingReply = null; // 当前正在编辑的回复对象

// 拖拽排序相关变量
let dragState = {
  isDragging: false,
  draggedElement: null,
  draggedIndex: -1,
  draggedType: null, // 'group' 或 'reply'
  placeholder: null,
  startX: 0,
  startY: 0,
  currentY: 0,
  offsetY: 0,
  container: null,
  items: [],
  animationFrame: null,
  potentialDrag: null // 用于检测是否开始拖拽
};

// DOM元素
const addGroupBtn = document.getElementById('add-group-btn');
const addReplyBtn = document.getElementById('add-reply-btn');
const groupsContainer = document.getElementById('groups-container');
const repliesContainer = document.getElementById('replies-container');
const currentGroupName = document.getElementById('current-group-name');
const importConfigBtn = document.getElementById('import-config-btn');
const exportConfigBtn = document.getElementById('export-config-btn');
const contextMenu = document.getElementById('context-menu');

// 模态框元素
const addGroupModal = document.getElementById('add-group-modal');
const addReplyModal = document.getElementById('add-reply-modal');
const editReplyModal = document.getElementById('edit-reply-modal');
const groupNameInput = document.getElementById('group-name');
const replyTitleInput = document.getElementById('reply-title');
const replyContentInput = document.getElementById('reply-content');
const replyImagesInput = document.getElementById('reply-images');
const editReplyTitleInput = document.getElementById('edit-reply-title');
const editReplyContentInput = document.getElementById('edit-reply-content');
const editReplyImagesInput = document.getElementById('edit-reply-images');
const imagesPreview = document.getElementById('images-preview');
const editImagesPreview = document.getElementById('edit-images-preview');
const saveGroupBtn = document.getElementById('save-group-btn');
const saveReplyBtn = document.getElementById('save-reply-btn');
const updateReplyBtn = document.getElementById('update-reply-btn');
const closeButtons = document.querySelectorAll('.close');

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  initDatabase().then(() => {
    loadData();
    setupEventListeners();

    // 初始化拖拽排序功能
    initDragAndDrop();

    // 初始化模态框位置
    positionModalInViewport(addGroupModal);
    positionModalInViewport(addReplyModal);
  }).catch(error => {
    console.error('数据库初始化失败:', error);
    alert('数据库初始化失败，请重新加载页面');
  });
});

// 初始化IndexedDB数据库
function initDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('TristanQuickReplyDB', 2); // 增加版本号以支持数据迁移

    request.onerror = function(event) {
      console.error('数据库打开失败:', event.target.error);
      reject(event.target.error);
    };

    request.onsuccess = function(event) {
      db = event.target.result;
      console.log('数据库连接成功');
      resolve();
    };

    request.onupgradeneeded = function(event) {
      const db = event.target.result;
      const oldVersion = event.oldVersion;

      // 创建存储组和回复数据的对象存储
      if (!db.objectStoreNames.contains('groups')) {
        db.createObjectStore('groups', { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains('replies')) {
        const repliesStore = db.createObjectStore('replies', { keyPath: 'id', autoIncrement: true });
        repliesStore.createIndex('groupId', 'groupId', { unique: false });
      }

      if (!db.objectStoreNames.contains('settings')) {
        db.createObjectStore('settings', { keyPath: 'key' });
      }

      // 版本2：添加排序字段的数据迁移
      if (oldVersion < 2) {
        console.log('开始数据迁移：添加排序字段');

        // 迁移分组数据，添加sortOrder字段
        const groupsTransaction = event.target.transaction;
        if (groupsTransaction && db.objectStoreNames.contains('groups')) {
          const groupsStore = groupsTransaction.objectStore('groups');
          const groupsRequest = groupsStore.getAll();

          groupsRequest.onsuccess = function() {
            const groups = groupsRequest.result;
            groups.forEach((group, index) => {
              if (group.sortOrder === undefined) {
                group.sortOrder = index;
                groupsStore.put(group);
              }
            });
          };
        }

        // 迁移回复数据，添加sortOrder字段
        if (groupsTransaction && db.objectStoreNames.contains('replies')) {
          const repliesStore = groupsTransaction.objectStore('replies');
          const repliesRequest = repliesStore.getAll();

          repliesRequest.onsuccess = function() {
            const replies = repliesRequest.result;
            // 按groupId分组，然后为每组内的回复分配sortOrder
            const groupedReplies = {};
            replies.forEach(reply => {
              if (!groupedReplies[reply.groupId]) {
                groupedReplies[reply.groupId] = [];
              }
              groupedReplies[reply.groupId].push(reply);
            });

            Object.keys(groupedReplies).forEach(groupId => {
              groupedReplies[groupId].forEach((reply, index) => {
                if (reply.sortOrder === undefined) {
                  reply.sortOrder = index;
                  repliesStore.put(reply);
                }
              });
            });
          };
        }
      }
    };
  });
}

// 加载数据
function loadData() {
  Promise.all([
    getGroups(),
    getAllReplies(),
    getSetting('lastSelectedGroupId')
  ]).then(([groupsData, repliesData, lastSelectedGroupId]) => {
    // 按sortOrder排序分组数据
    groups = (groupsData || []).sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

    // 转换回复数据为之前的格式，并按sortOrder排序
    replies = {};
    repliesData.forEach(reply => {
      if (!replies[reply.groupId]) {
        replies[reply.groupId] = [];
      }
      replies[reply.groupId].push({
        id: reply.id,
        title: reply.title,
        content: reply.content,
        images: reply.images || (reply.image ? [reply.image] : []), // 兼容旧数据
        sortOrder: reply.sortOrder || 0
      });
    });

    // 对每个分组内的回复按sortOrder排序
    Object.keys(replies).forEach(groupId => {
      replies[groupId].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    });
    
    renderGroups();
    
    // 如果有上次选择的分组且该分组仍然存在，则选择它
    if (lastSelectedGroupId && groups.some(group => group.id === lastSelectedGroupId)) {
      selectGroup(lastSelectedGroupId);
    } 
    // 否则如果有分组，默认选择第一个
    else if (groups.length > 0) {
      selectGroup(groups[0].id);
    } else {
      repliesContainer.innerHTML = '<div class="empty-message">请先创建一个分组</div>';
      addReplyBtn.disabled = true;
    }
  }).catch(error => {
    console.error('加载数据失败:', error);
    alert('加载数据失败，请重新加载页面');
  });
}

// 从IndexedDB获取所有分组
function getGroups() {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['groups'], 'readonly');
    const store = transaction.objectStore('groups');
    const request = store.getAll();
    
    request.onsuccess = function(event) {
      resolve(event.target.result);
    };
    
    request.onerror = function(event) {
      console.error('获取分组失败:', event.target.error);
      reject(event.target.error);
    };
  });
}

// 从IndexedDB获取所有回复
function getAllReplies() {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['replies'], 'readonly');
    const store = transaction.objectStore('replies');
    const request = store.getAll();
    
    request.onsuccess = function(event) {
      resolve(event.target.result);
    };
    
    request.onerror = function(event) {
      console.error('获取回复失败:', event.target.error);
      reject(event.target.error);
    };
  });
}

// 从IndexedDB获取特定分组的回复
function getRepliesByGroupId(groupId) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['replies'], 'readonly');
    const store = transaction.objectStore('replies');
    const index = store.index('groupId');
    const request = index.getAll(groupId);
    
    request.onsuccess = function(event) {
      resolve(event.target.result);
    };
    
    request.onerror = function(event) {
      console.error('获取分组回复失败:', event.target.error);
      reject(event.target.error);
    };
  });
}

// 获取设置
function getSetting(key) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['settings'], 'readonly');
    const store = transaction.objectStore('settings');
    const request = store.get(key);
    
    request.onsuccess = function(event) {
      resolve(event.target.result ? event.target.result.value : null);
    };
    
    request.onerror = function(event) {
      console.error('获取设置失败:', event.target.error);
      reject(event.target.error);
    };
  });
}

// 保存设置
function saveSetting(key, value) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['settings'], 'readwrite');
    const store = transaction.objectStore('settings');
    const request = store.put({ key, value });
    
    request.onsuccess = function(event) {
      resolve();
    };
    
    request.onerror = function(event) {
      console.error('保存设置失败:', event.target.error);
      reject(event.target.error);
    };
  });
}

// 设置事件监听器
function setupEventListeners() {
  // 添加分组按钮
  addGroupBtn.addEventListener('click', () => {
    clearInputs(); // 清空之前的输入
    addGroupModal.style.display = 'block';
    groupNameInput.focus();
  });
  
  // 添加快捷回复按钮
  addReplyBtn.addEventListener('click', () => {
    if (!currentGroupId) {
      alert('请先选择一个分组');
      return;
    }
    
    clearInputs(); // 清空之前的输入
    addReplyModal.style.display = 'block';
    replyTitleInput.focus();
    
    // 确保模态框在视口内完全可见
    positionModalInViewport(addReplyModal);
  });
  
  // 保存分组按钮
  saveGroupBtn.addEventListener('click', saveGroup);
  
  // 保存快捷回复按钮
  saveReplyBtn.addEventListener('click', saveReply);
  
  // 关闭模态框按钮
  closeButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.target.closest('.modal').style.display = 'none';
      if (e.target.closest('.modal').id === 'edit-reply-modal') {
        clearEditInputs();
      } else {
        clearInputs();
      }
    });
  });
  
  // 图片上传预览
  replyImagesInput.addEventListener('change', handleImagesUpload);
  
  // 窗口大小改变时重新定位模态框
  window.addEventListener('resize', () => {
    if (addReplyModal.style.display === 'block') {
      positionModalInViewport(addReplyModal);
    }
    if (addGroupModal.style.display === 'block') {
      positionModalInViewport(addGroupModal);
    }
  });
  
  // 导入配置按钮
  importConfigBtn.addEventListener('click', importConfig);
  
  // 导出配置按钮
  exportConfigBtn.addEventListener('click', exportConfig);
  
  // 显示上下文菜单
  contextMenu.addEventListener('click', (e) => {
    e.stopPropagation();
    showContextMenu(e, 'group', currentGroupId);
  });
  
  // 编辑图片上传预览
  editReplyImagesInput.addEventListener('change', handleEditImagesUpload);

  // 添加图片双击删除事件委托
  imagesPreview.addEventListener('dblclick', handleImageRemove);
  editImagesPreview.addEventListener('dblclick', handleEditImageRemove);
}

// 渲染分组
function renderGroups() {
  groupsContainer.innerHTML = '';
  
  if (groups.length === 0) {
    groupsContainer.innerHTML = '<div class="empty-message">没有分组</div>';
    return;
  }
  
  groups.forEach(group => {
    const groupElement = document.createElement('div');
    groupElement.className = 'group-item';
    if (group.id === currentGroupId) {
      groupElement.classList.add('active');
    }

    groupElement.innerHTML = `
      <span class="group-name">${group.name}</span>
      <button class="delete-btn" data-id="${group.id}">×</button>
    `;

    // 不再需要点击事件监听器，因为现在由拖拽系统处理

    groupsContainer.appendChild(groupElement);
  });
}

// 渲染快捷回复
function renderReplies(groupId) {
  repliesContainer.innerHTML = '';
  
  const groupReplies = replies[groupId] || [];
  
  if (groupReplies.length === 0) {
    repliesContainer.innerHTML = '<div class="empty-message">此分组没有快捷回复</div>';
    return;
  }
  
  groupReplies.forEach(reply => {
    const replyElement = document.createElement('div');
    replyElement.className = 'reply-item';

    // 创建图片容器
    const imageContainer = document.createElement('div');
    imageContainer.className = 'reply-image-container';

    // 创建内容容器
    const contentContainer = document.createElement('div');
    contentContainer.className = 'reply-content-container';

    // 添加标题和内容到内容容器
    contentContainer.innerHTML = `
      <div class="reply-title">${reply.title}</div>
      <div class="reply-content">${reply.content}</div>
    `;

    // 如果有图片，添加到图片容器
    const images = reply.images || (reply.image ? [reply.image] : []); // 兼容旧数据
    if (images && images.length > 0) {
      renderReplyImages(imageContainer, images);
    }

    // 添加菜单按钮
    const menuBtn = document.createElement('button');
    menuBtn.className = 'menu-btn';
    menuBtn.setAttribute('data-id', reply.id);
    menuBtn.setAttribute('data-type', 'reply');

    // 将所有元素添加到回复项中
    replyElement.appendChild(imageContainer);
    replyElement.appendChild(contentContainer);
    replyElement.appendChild(menuBtn);

    // 事件处理
    menuBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      showContextMenu(e, 'reply', reply.id, groupId);
    });

    // 不再需要点击事件监听器，因为现在由拖拽系统处理

    repliesContainer.appendChild(replyElement);
  });
}

// 选择分组
function selectGroup(groupId) {
  currentGroupId = groupId;
  
  // 更新UI
  const groupItems = document.querySelectorAll('.group-item');
  groupItems.forEach(item => {
    item.classList.remove('active');
    if (item.querySelector(`button[data-id="${groupId}"]`)) {
      item.classList.add('active');
    }
  });
  
  // 更新当前分组名称
  const group = groups.find(g => g.id === groupId);
  if (group) {
    currentGroupName.textContent = group.name;
  }
  
  // 启用添加回复按钮
  addReplyBtn.disabled = false;
  
  // 渲染该分组的快捷回复
  renderReplies(groupId);
  
  // 保存最后选择的分组ID
  saveSetting('lastSelectedGroupId', groupId);
}

// 渲染回复项中的图片
function renderReplyImages(container, images) {
  if (!images || images.length === 0) {
    container.innerHTML = '';
    return;
  }

  // 在快捷回复列表中，只显示第一张图片
  container.innerHTML = `<img src="${images[0]}" class="reply-image" alt="回复图片">`;
}

// 保存分组
function saveGroup() {
  const groupName = groupNameInput.value.trim();
  
  if (!groupName) {
    alert('请输入分组名称');
    return;
  }
  
  const newGroup = {
    id: Date.now().toString(),
    name: groupName,
    sortOrder: groups.length // 新分组排在最后
  };
  
  // 保存到IndexedDB
  const transaction = db.transaction(['groups'], 'readwrite');
  const store = transaction.objectStore('groups');
  const request = store.add(newGroup);
  
  request.onsuccess = function() {
    groups.push(newGroup);
    addGroupModal.style.display = 'none';
    clearInputs();
    renderGroups();
    
    // 如果这是第一个分组，选择它
    if (groups.length === 1) {
      selectGroup(newGroup.id);
    }
  };
  
  request.onerror = function(event) {
    console.error('保存分组失败:', event.target.error);
    alert('保存分组失败，请重试');
  };
}

// 删除分组
function deleteGroup(groupId) {
  if (confirm('确定要删除此分组吗？所有相关的快捷回复也将被删除。')) {
    // 删除分组
    const groupTransaction = db.transaction(['groups'], 'readwrite');
    const groupStore = groupTransaction.objectStore('groups');
    groupStore.delete(groupId);
    
    // 删除该分组下的所有回复
    const replyTransaction = db.transaction(['replies'], 'readwrite');
    const replyStore = replyTransaction.objectStore('replies');
    const replyIndex = replyStore.index('groupId');
    const replyRequest = replyIndex.openCursor(groupId);
    
    replyRequest.onsuccess = function(event) {
      const cursor = event.target.result;
      if (cursor) {
        replyStore.delete(cursor.value.id);
        cursor.continue();
      }
    };
    
    // 是否删除的是当前选中的分组
    const isCurrentGroup = (currentGroupId === groupId);
    
    // 更新内存中的数据
    groups = groups.filter(group => group.id !== groupId);
    delete replies[groupId];
    
    // 重新渲染UI
    renderGroups();
    
    // 如果当前选中的分组被删除
    if (isCurrentGroup) {
      if (groups.length > 0) {
        // 选择第一个分组
        selectGroup(groups[0].id);
      } else {
        currentGroupId = null;
        // 清除lastSelectedGroupId
        saveSetting('lastSelectedGroupId', null);
        repliesContainer.innerHTML = '<div class="empty-message">请先创建一个分组</div>';
        currentGroupName.textContent = '快捷回复';
        addReplyBtn.disabled = true;
      }
    }
  }
}

// 处理多图片上传
function handleImagesUpload(e) {
  const files = Array.from(e.target.files);
  const fileNameDisplay = document.getElementById('file-name-display');

  if (!files || files.length === 0) {
    return; // 如果没有选择文件，保持现有状态
  }

  // 检查文件类型
  const validFiles = files.filter(file => file.type.match('image.*'));
  if (validFiles.length !== files.length) {
    alert('请只上传图片文件');
  }

  if (validFiles.length === 0) {
    return; // 如果没有有效文件，保持现有状态
  }

  // 将新文件添加到现有文件数组中（保持顺序）
  imageFiles = [...imageFiles, ...validFiles];

  // 显示文件数量
  fileNameDisplay.textContent = `已选择 ${imageFiles.length} 个文件`;

  renderImagesPreview(imagesPreview, imageFiles);
}

// 渲染图片预览
function renderImagesPreview(container, files) {
  container.innerHTML = '';

  // 创建占位符确保顺序
  const placeholders = files.map((file, index) => {
    const previewItem = document.createElement('div');
    previewItem.className = 'image-preview-item';
    previewItem.innerHTML = `
      <div style="width: 100%; height: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999;">
        加载中...
      </div>
    `;
    container.appendChild(previewItem);
    return previewItem;
  });

  // 按顺序加载图片
  files.forEach((file, index) => {
    const reader = new FileReader();
    reader.onload = function(event) {
      placeholders[index].innerHTML = `
        <img src="${event.target.result}" alt="预览图片${index + 1}">
      `;
      placeholders[index].setAttribute('data-index', index);
    };
    reader.readAsDataURL(file);
  });
}

// 处理添加快捷回复时的图片删除
function handleImageRemove(e) {
  // 双击图片或图片容器删除
  let targetElement = e.target;
  let index = null;

  // 如果点击的是图片，获取父容器的索引
  if (targetElement.tagName === 'IMG') {
    targetElement = targetElement.closest('.image-preview-item');
  }

  // 如果点击的是图片容器，获取索引
  if (targetElement && targetElement.classList.contains('image-preview-item')) {
    index = parseInt(targetElement.getAttribute('data-index'));
    if (!isNaN(index)) {
      removeImageFromPreview(index);
    }
  }
}

// 处理编辑快捷回复时的图片删除
function handleEditImageRemove(e) {
  // 双击图片或图片容器删除
  let targetElement = e.target;
  let index = null;
  let type = null;

  // 如果点击的是图片，获取父容器的索引和类型
  if (targetElement.tagName === 'IMG') {
    targetElement = targetElement.closest('.image-preview-item');
  }

  // 如果点击的是图片容器，获取索引和类型
  if (targetElement && targetElement.classList.contains('image-preview-item')) {
    index = parseInt(targetElement.getAttribute('data-index'));
    type = targetElement.getAttribute('data-type');

    if (!isNaN(index)) {
      if (type === 'existing') {
        // 删除已有图片
        removeExistingImage(index);
      } else if (type === 'new') {
        // 删除新上传的图片
        removeNewImageFromEdit(index);
      }
    }
  }
}

// 删除已有图片
function removeExistingImage(index) {
  // 从当前编辑的回复中删除图片
  if (currentEditingReply && currentEditingReply.images) {
    currentEditingReply.images.splice(index, 1);
    updateEditFileDisplay();
    renderEditPreviewWithBothTypes();
  }
}

// 删除新上传的图片
function removeNewImageFromEdit(index) {
  editImageFiles.splice(index, 1);
  updateEditFileDisplay();
  renderEditPreviewWithBothTypes();
}

// 更新编辑文件显示信息
function updateEditFileDisplay() {
  const fileNameDisplay = document.getElementById('edit-file-name-display');
  const existingCount = currentEditingReply && currentEditingReply.images ? currentEditingReply.images.length : 0;
  const newCount = editImageFiles.length;
  const totalCount = existingCount + newCount;

  if (totalCount === 0) {
    fileNameDisplay.textContent = '未选择文件';
    editImagesPreview.innerHTML = '';
    editReplyImagesInput.value = '';
  } else {
    fileNameDisplay.textContent = `共 ${totalCount} 张图片`;
  }
}

// 从预览中删除图片
function removeImageFromPreview(index) {
  imageFiles.splice(index, 1);
  const fileNameDisplay = document.getElementById('file-name-display');

  if (imageFiles.length === 0) {
    imagesPreview.innerHTML = '';
    fileNameDisplay.textContent = '未选择文件';
    replyImagesInput.value = '';
  } else {
    fileNameDisplay.textContent = `已选择 ${imageFiles.length} 个文件`;
    renderImagesPreview(imagesPreview, imageFiles);
  }
}



// 保存快捷回复
function saveReply() {
  const replyTitle = replyTitleInput.value.trim();
  const replyContent = replyContentInput.value.trim();
  
  if (!replyTitle) {
    alert('请输入标题');
    return;
  }

  if (!replyContent) {
    alert('请输入内容');
    return;
  }
  
  const newReply = {
    id: Date.now().toString(),
    groupId: currentGroupId,
    title: replyTitle,
    content: replyContent,
    images: [],
    sortOrder: replies[currentGroupId] ? replies[currentGroupId].length : 0 // 新回复排在最后
  };

  // 如果有图片，处理图片
  if (imageFiles && imageFiles.length > 0) {
    let processedCount = 0;
    const totalFiles = imageFiles.length;

    imageFiles.forEach((file, index) => {
      const reader = new FileReader();
      reader.onload = function(event) {
        newReply.images[index] = event.target.result; // Base64编码的图片
        processedCount++;

        // 所有图片都处理完成后保存
        if (processedCount === totalFiles) {
          saveReplyToStorage(newReply);
        }
      };
      reader.readAsDataURL(file);
    });
  } else {
    saveReplyToStorage(newReply);
  }
}

// 保存快捷回复到存储
function saveReplyToStorage(reply) {
  // 保存到IndexedDB
  const transaction = db.transaction(['replies'], 'readwrite');
  const store = transaction.objectStore('replies');
  const request = store.add(reply);
  
  request.onsuccess = function() {
    // 确保当前分组的回复数组已存在
    if (!replies[reply.groupId]) {
      replies[reply.groupId] = [];
    }
    
    // 更新内存中的数据
    replies[reply.groupId].push(reply);
    
    addReplyModal.style.display = 'none';
    clearInputs();
    renderReplies(reply.groupId);
  };
  
  request.onerror = function(event) {
    console.error('保存回复失败:', event.target.error);
    alert('保存回复失败，请重试');
  };
}

// 显示上下文菜单
function showContextMenu(event, type, itemId, groupId = null) {
  // 保存当前项目信息
  currentEditingItemId = itemId;
  isEditingGroup = (type === 'group');
  
  // 设置菜单位置
  const rect = event.target.getBoundingClientRect();
  contextMenu.style.top = `${rect.bottom + 5}px`;
  contextMenu.style.left = `${rect.left}px`;
  
  // 显示菜单
  contextMenu.classList.add('active');
  
  // 设置菜单项点击事件
  const menuItems = contextMenu.querySelectorAll('.context-menu-item');
  menuItems.forEach(item => {
    const action = item.getAttribute('data-action');
    
    item.onclick = (e) => {
      e.stopPropagation(); // 阻止事件冒泡
      
      if (action === 'edit') {
        if (type === 'group') {
          // 分组不支持编辑
          //alert('分组不支持编辑，请删除后重新创建');
        } else {
          editReply(groupId, itemId);
        }
      } else if (action === 'delete') {
        if (type === 'group') {
          deleteGroup(itemId);
        } else {
          deleteReply(groupId, itemId);
        }
      }
      
      // 隐藏菜单
      hideContextMenu();
    };
  });
  
  // 点击其他区域隐藏菜单
  setTimeout(() => {
    document.addEventListener('click', hideContextMenuOnClickOutside);
  }, 0);
}

// 隐藏上下文菜单
function hideContextMenu() {
  contextMenu.classList.remove('active');
  document.removeEventListener('click', hideContextMenuOnClickOutside);
}

// 点击外部区域时隐藏菜单
function hideContextMenuOnClickOutside(event) {
  if (!contextMenu.contains(event.target) && !event.target.classList.contains('menu-btn')) {
    hideContextMenu();
  }
}

// 编辑快捷回复
function editReply(groupId, replyId) {
  const reply = replies[groupId].find(r => r.id === replyId);
  if (!reply) return;

  // 保存当前编辑的回复（深拷贝）
  currentEditingReply = {
    ...reply,
    images: reply.images ? [...reply.images] : (reply.image ? [reply.image] : [])
  };

  // 填充表单
  editReplyTitleInput.value = reply.title;
  editReplyContentInput.value = reply.content;

  // 处理图片数据（兼容旧数据）
  const images = currentEditingReply.images;
  editImageFiles = []; // 清空编辑图片文件数组

  // 显示预览
  updateEditFileDisplay();
  renderEditPreviewWithBothTypes();

  // 显示编辑模态框
  editReplyModal.style.display = 'block';
  editReplyTitleInput.focus();

  // 确保模态框在视口内完全可见
  positionModalInViewport(editReplyModal);

  // 设置更新按钮点击事件
  updateReplyBtn.onclick = () => updateReply(groupId, replyId);
}



// 处理编辑时的多图片上传
function handleEditImagesUpload(e) {
  const files = Array.from(e.target.files);
  const fileNameDisplay = document.getElementById('edit-file-name-display');

  if (!files || files.length === 0) {
    // 如果没有选择新文件，保持原有状态
    return;
  }

  // 检查文件类型
  const validFiles = files.filter(file => file.type.match('image.*'));
  if (validFiles.length !== files.length) {
    alert('请只上传图片文件');
  }

  if (validFiles.length === 0) {
    return;
  }

  // 将新文件添加到现有文件数组中
  editImageFiles = [...editImageFiles, ...validFiles];

  // 更新显示信息
  const existingCount = currentEditingReply && currentEditingReply.images ? currentEditingReply.images.length : 0;
  const totalCount = existingCount + editImageFiles.length;
  fileNameDisplay.textContent = `共 ${totalCount} 张图片`;

  // 重新渲染预览（显示已有图片 + 新图片）
  renderEditPreviewWithBothTypes();
}

// 渲染编辑预览（包含已有图片和新图片）
function renderEditPreviewWithBothTypes() {
  editImagesPreview.innerHTML = '';

  // 首先渲染已有图片
  if (currentEditingReply && currentEditingReply.images) {
    currentEditingReply.images.forEach((image, index) => {
      const previewItem = document.createElement('div');
      previewItem.className = 'image-preview-item';
      previewItem.setAttribute('data-index', index);
      previewItem.setAttribute('data-type', 'existing');
      previewItem.innerHTML = `<img src="${image}" alt="已有图片${index + 1}">`;
      editImagesPreview.appendChild(previewItem);
    });
  }

  // 然后渲染新上传的图片
  editImageFiles.forEach((file, index) => {
    const reader = new FileReader();
    reader.onload = function(event) {
      const previewItem = document.createElement('div');
      previewItem.className = 'image-preview-item';
      previewItem.setAttribute('data-index', index);
      previewItem.setAttribute('data-type', 'new');
      previewItem.innerHTML = `<img src="${event.target.result}" alt="新图片${index + 1}">`;
      editImagesPreview.appendChild(previewItem);
    };
    reader.readAsDataURL(file);
  });
}



// 更新快捷回复
function updateReply(groupId, replyId) {
  const replyTitle = editReplyTitleInput.value.trim();
  const replyContent = editReplyContentInput.value.trim();
  
  if (!replyTitle) {
    alert('请输入标题');
    return;
  }

  if (!replyContent) {
    alert('请输入内容');
    return;
  }
  
  const replyIndex = replies[groupId].findIndex(r => r.id === replyId);
  if (replyIndex === -1) return;
  
  const originalReply = replies[groupId][replyIndex];
  
  const updatedReply = {
    id: replyId,
    groupId: groupId,
    title: replyTitle,
    content: replyContent,
    images: currentEditingReply ? currentEditingReply.images : (originalReply.images || (originalReply.image ? [originalReply.image] : [])), // 使用当前编辑的图片数据
    sortOrder: originalReply.sortOrder || 0 // 保留原有的排序
  };

  // 如果有新图片，添加到现有图片中
  if (editImageFiles && editImageFiles.length > 0) {
    let processedCount = 0;
    const totalFiles = editImageFiles.length;
    const newImages = [];

    editImageFiles.forEach((file, index) => {
      const reader = new FileReader();
      reader.onload = function(event) {
        newImages[index] = event.target.result; // Base64编码的图片
        processedCount++;

        // 所有新图片都处理完成后，合并到现有图片中
        if (processedCount === totalFiles) {
          updatedReply.images = [...updatedReply.images, ...newImages];
          saveUpdatedReply(groupId, replyIndex, updatedReply);
        }
      };
      reader.readAsDataURL(file);
    });
  } else {
    saveUpdatedReply(groupId, replyIndex, updatedReply);
  }
}

// 保存更新的快捷回复到存储
function saveUpdatedReply(groupId, replyIndex, updatedReply) {
  // 保存到IndexedDB
  const transaction = db.transaction(['replies'], 'readwrite');
  const store = transaction.objectStore('replies');
  const request = store.put(updatedReply);
  
  request.onsuccess = function() {
    // 更新内存中的数据
    replies[groupId][replyIndex] = updatedReply;
    
    editReplyModal.style.display = 'none';
    clearEditInputs();
    renderReplies(groupId);
  };
  
  request.onerror = function(event) {
    console.error('更新回复失败:', event.target.error);
    alert('更新回复失败，请重试');
  };
}

// 发送到WhatsApp
function sendToWhatsApp(reply) {
  // 添加发送状态提示
  const statusEl = document.createElement('div');
  statusEl.className = 'send-status';
  statusEl.textContent = '正在发送...';
  statusEl.style.position = 'fixed';
  statusEl.style.top = '50%';
  statusEl.style.left = '50%';
  statusEl.style.transform = 'translate(-50%, -50%)';
  statusEl.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  statusEl.style.color = 'white';
  statusEl.style.padding = '10px 20px';
  statusEl.style.borderRadius = '5px';
  statusEl.style.zIndex = '9999';
  document.body.appendChild(statusEl);
  
  // 发送消息到content script
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'sendReply',
      reply: reply
    }, (response) => {
      // 移除状态提示
      document.body.removeChild(statusEl);
      
      if (chrome.runtime.lastError) {
        alert('请在指定网页中使用此功能~ WhatsApp,Business');
      } else if (response && response.success) {
        // 成功发送
        const successEl = document.createElement('div');
        successEl.className = 'send-status success';
        successEl.textContent = '发送成功!';
        successEl.style.position = 'fixed';
        successEl.style.top = '50%';
        successEl.style.left = '50%';
        successEl.style.transform = 'translate(-50%, -50%)';
        successEl.style.backgroundColor = 'rgba(0, 168, 132, 0.7)';
        successEl.style.color = 'white';
        successEl.style.padding = '10px 20px';
        successEl.style.borderRadius = '5px';
        successEl.style.zIndex = '9999';
        document.body.appendChild(successEl);
        
        // 短暂显示成功提示后关闭
        setTimeout(() => {
          document.body.removeChild(successEl);
          // 不再自动关闭窗口
        }, 800);
      } else {
        // 发送失败
        const errorMessage = response && response.error ? response.error : '发送失败，请重试';
        alert('发送失败: ' + errorMessage);
      }
    });
  });
}

// 清空编辑输入
function clearEditInputs() {
  editReplyTitleInput.value = '';
  editReplyContentInput.value = '';
  editReplyImagesInput.value = '';
  editImagesPreview.innerHTML = '';
  editImageFiles = []; // 清空编辑图片文件数组
  currentEditingReply = null; // 清空当前编辑的回复
  document.getElementById('edit-file-name-display').textContent = '未选择文件';
}

// 清空输入
function clearInputs() {
  groupNameInput.value = '';
  replyTitleInput.value = '';
  replyContentInput.value = '';
  replyImagesInput.value = '';
  imagesPreview.innerHTML = '';
  imageFiles = []; // 清空图片文件数组

  // 重置文件名显示
  const fileNameDisplay = document.getElementById('file-name-display');
  if (fileNameDisplay) {
    fileNameDisplay.textContent = '未选择文件';
  }
}

// 确保模态框在视口内完全可见
function positionModalInViewport(modal) {
  const modalContent = modal.id === 'add-group-modal' 
    ? document.getElementById('group-modal-content') 
    : document.getElementById('reply-modal-content');
    
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;
  const modalHeight = modalContent.offsetHeight;
  const modalWidth = modalContent.offsetWidth;
  
  // 计算可用的最大高度（视口高度的85%）
  const maxAvailableHeight = viewportHeight * 0.85;
  
  // 如果模态框高度超过可用高度，调整其最大高度
  if (modalHeight > maxAvailableHeight) {
    modalContent.style.maxHeight = maxAvailableHeight + 'px';
    modalContent.style.overflowY = 'auto';
    
    // 不再单独调整图片预览区域的样式，保持CSS中定义的一致样式
  } else {
    // 如果高度合适，不需要滚动条
    modalContent.style.overflowY = '';
  }
  
  // 调整模态框垂直和水平位置，确保居中
  modalContent.style.margin = '0';
  modalContent.style.position = 'fixed';
  modalContent.style.top = '50%';
  modalContent.style.left = '50%';
  modalContent.style.transform = 'translate(-50%, -50%)';
}

// 导出配置
function exportConfig() {
  // 准备要导出的数据
  Promise.all([
    getGroups(),
    getAllReplies()
  ]).then(([groupsData, repliesData]) => {
    const configData = {
      groups: groupsData,
      replies: repliesData,
      version: '2.0' // 更新版本号以支持排序
    };
    
    // 转换为JSON字符串
    const jsonData = JSON.stringify(configData, null, 2);
    
    // 创建Blob对象
    const blob = new Blob([jsonData], { type: 'application/json' });
    
    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'QuickReply_Config.json';
    
    // 触发下载
    document.body.appendChild(a);
    a.click();
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);
  }).catch(error => {
    console.error('导出配置失败:', error);
    alert('导出配置失败，请重试');
  });
}

// 导入配置
function importConfig() {
  // 创建文件输入元素
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = 'application/json';
  
  // 监听文件选择
  fileInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(event) {
      try {
        // 解析JSON
        const configData = JSON.parse(event.target.result);
        
        // 验证数据格式
        if (!configData.groups || !configData.replies) {
          throw new Error('无效的配置文件格式');
        }
        
        // 确认导入
        if (confirm('导入将清空现有的所有分组和快捷回复，确定要继续吗？')) {
          // 清空现有数据
          clearDatabase().then(() => {
            // 导入新数据
            return importDatabaseData(configData.groups, configData.replies);
          }).then(() => {
            // 重新加载数据
            loadData();
            alert('配置导入成功！');
          }).catch(error => {
            console.error('导入失败:', error);
            alert('导入失败: ' + error.message);
          });
        }
      } catch (error) {
        alert('导入失败: ' + error.message);
      }
    };
    
    reader.readAsText(file);
  });
  
  // 触发文件选择
  fileInput.click();
}

// 清空数据库
function clearDatabase() {
  return new Promise((resolve, reject) => {
    // 清空分组
    const groupsTransaction = db.transaction(['groups'], 'readwrite');
    const groupsStore = groupsTransaction.objectStore('groups');
    const groupsClearRequest = groupsStore.clear();
    
    groupsClearRequest.onerror = function(event) {
      reject(event.target.error);
      return;
    };
    
    // 清空回复
    const repliesTransaction = db.transaction(['replies'], 'readwrite');
    const repliesStore = repliesTransaction.objectStore('replies');
    const repliesClearRequest = repliesStore.clear();
    
    repliesClearRequest.onsuccess = function() {
      resolve();
    };
    
    repliesClearRequest.onerror = function(event) {
      reject(event.target.error);
    };
  });
}

// 导入数据库数据
function importDatabaseData(groupsData, repliesData) {
  return new Promise((resolve, reject) => {
    // 导入分组
    const groupsTransaction = db.transaction(['groups'], 'readwrite');
    const groupsStore = groupsTransaction.objectStore('groups');

    for (const group of groupsData) {
      // 确保分组有sortOrder字段
      if (group.sortOrder === undefined) {
        group.sortOrder = 0;
      }
      groupsStore.add(group);
    }

    // 导入回复
    const repliesTransaction = db.transaction(['replies'], 'readwrite');
    const repliesStore = repliesTransaction.objectStore('replies');

    for (const reply of repliesData) {
      // 确保回复有sortOrder字段
      if (reply.sortOrder === undefined) {
        reply.sortOrder = 0;
      }
      repliesStore.add(reply);
    }

    repliesTransaction.oncomplete = function() {
      resolve();
    };

    repliesTransaction.onerror = function(event) {
      reject(event.target.error);
    };
  });
}

// 删除快捷回复
function deleteReply(groupId, replyId) {
  // 从IndexedDB中删除
  const transaction = db.transaction(['replies'], 'readwrite');
  const store = transaction.objectStore('replies');
  const request = store.delete(replyId);

  request.onsuccess = function() {
    // 更新内存中的数据
    replies[groupId] = replies[groupId].filter(reply => reply.id !== replyId);
    renderReplies(groupId);
  };

  request.onerror = function(event) {
    console.error('删除回复失败:', event.target.error);
    alert('删除回复失败，请重试');
  };
}

// ==================== 拖拽排序功能 ====================

// 初始化拖拽排序功能
function initDragAndDrop() {
  // 为分组容器添加拖拽功能
  initContainerDragAndDrop(groupsContainer, 'group');
  // 为回复容器添加拖拽功能
  initContainerDragAndDrop(repliesContainer, 'reply');
}

// 为容器初始化拖拽功能
function initContainerDragAndDrop(container, type) {
  container.addEventListener('mousedown', (e) => handleMouseDown(e, type));
  container.addEventListener('dragstart', (e) => e.preventDefault()); // 禁用默认拖拽
}

// 处理鼠标按下事件
function handleMouseDown(e, type) {
  // 检查是否点击了按钮或其他交互元素
  if (e.target.closest('.delete-btn, .menu-btn, button, input, textarea, select')) {
    return; // 如果点击的是按钮等交互元素，不启动拖拽
  }

  const item = e.target.closest(type === 'group' ? '.group-item' : '.reply-item');
  if (!item) return;

  // 记录初始位置，但不立即开始拖拽
  dragState.startX = e.clientX;
  dragState.startY = e.clientY;
  dragState.potentialDrag = {
    element: item,
    type: type,
    startTime: Date.now()
  };

  // 添加临时事件监听器来检测是否开始拖拽
  document.addEventListener('mousemove', handlePotentialDragMove);
  document.addEventListener('mouseup', handlePotentialDragEnd);
}

// 处理潜在拖拽的鼠标移动
function handlePotentialDragMove(e) {
  if (!dragState.potentialDrag) return;

  const deltaX = Math.abs(e.clientX - dragState.startX);
  const deltaY = Math.abs(e.clientY - dragState.startY);
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

  // 如果移动距离超过阈值，开始拖拽
  if (distance > 5) {
    startActualDrag(e);
  }
}

// 处理潜在拖拽的鼠标释放
function handlePotentialDragEnd(e) {
  if (dragState.potentialDrag) {
    // 如果没有开始实际拖拽，则处理点击事件
    const item = dragState.potentialDrag.element;
    const type = dragState.potentialDrag.type;

    // 清理潜在拖拽状态
    cleanupPotentialDrag();

    // 如果是短时间内的点击，执行原有的点击逻辑
    if (Date.now() - dragState.potentialDrag.startTime < 200) {
      handleItemClick(e, item, type);
    }
  }
}

// 开始实际拖拽
function startActualDrag(e) {
  if (!dragState.potentialDrag) return;

  const item = dragState.potentialDrag.element;
  const type = dragState.potentialDrag.type;

  // 清理潜在拖拽的事件监听器
  cleanupPotentialDrag();

  // 检查是否已经在拖拽中
  if (dragState.isDragging) {
    console.warn('已经在拖拽中，忽略新的拖拽请求');
    return;
  }

  // 检查容器是否有效
  const container = item.parentElement;
  if (!container) {
    console.error('无法找到容器元素');
    return;
  }

  // 检查是否有足够的项目进行排序
  const items = Array.from(container.children);
  if (items.length <= 1) {
    console.log('只有一个项目，无需排序');
    return;
  }

  // 初始化拖拽状态
  dragState.isDragging = true;
  dragState.draggedElement = item;
  dragState.draggedType = type;
  dragState.startY = e.clientY;
  dragState.currentY = e.clientY;
  dragState.container = container;
  dragState.items = items;
  dragState.draggedIndex = items.indexOf(item);

  // 计算鼠标相对于元素的偏移
  const rect = item.getBoundingClientRect();
  dragState.offsetY = e.clientY - rect.top;

  // 创建占位符
  createPlaceholder(item);

  // 添加拖拽样式
  item.classList.add('dragging');
  document.body.classList.add('dragging-active');

  // 添加全局事件监听器
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);

  // 禁用文本选择
  document.body.style.userSelect = 'none';
}

// 清理潜在拖拽状态
function cleanupPotentialDrag() {
  document.removeEventListener('mousemove', handlePotentialDragMove);
  document.removeEventListener('mouseup', handlePotentialDragEnd);
  dragState.potentialDrag = null;
  dragState.startX = 0;
  dragState.startY = 0;
}

// 处理项目点击事件
function handleItemClick(e, item, type) {
  if (type === 'group') {
    // 检查是否点击了删除按钮
    if (e.target.closest('.delete-btn')) {
      const groupId = e.target.closest('.delete-btn').getAttribute('data-id');
      deleteGroup(groupId);
    } else {
      // 获取分组ID并选择分组
      const groupId = item.querySelector('.delete-btn').getAttribute('data-id');
      selectGroup(groupId);
    }
  } else if (type === 'reply') {
    // 检查是否点击了菜单按钮
    if (e.target.closest('.menu-btn')) {
      const replyId = e.target.closest('.menu-btn').getAttribute('data-id');
      showContextMenu(e, 'reply', replyId, currentGroupId);
    } else {
      // 发送回复
      const replyId = item.querySelector('.menu-btn').getAttribute('data-id');
      const reply = replies[currentGroupId].find(r => r.id === replyId);
      if (reply) {
        sendToWhatsApp(reply);
      }
    }
  }
}

// 处理鼠标移动事件
function handleMouseMove(e) {
  if (!dragState.isDragging) return;

  e.preventDefault();
  dragState.currentY = e.clientY;

  // 使用requestAnimationFrame优化性能
  if (!dragState.animationFrame) {
    dragState.animationFrame = requestAnimationFrame(() => {
      // 更新拖拽元素位置
      updateDraggedElementPosition();

      // 更新占位符位置
      updatePlaceholderPosition();

      dragState.animationFrame = null;
    });
  }
}

// 处理鼠标释放事件
function handleMouseUp(e) {
  if (!dragState.isDragging) return;

  e.preventDefault();

  // 计算新的位置
  const newIndex = calculateNewIndex();

  // 执行排序更新
  if (newIndex !== dragState.draggedIndex && newIndex !== -1) {
    updateSortOrder(dragState.draggedType, dragState.draggedIndex, newIndex);
  }

  // 清理拖拽状态
  cleanupDragState();

  // 移除全局事件监听器
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);

  // 恢复文本选择
  document.body.style.userSelect = '';
}

// 创建占位符
function createPlaceholder(item) {
  dragState.placeholder = document.createElement('div');
  dragState.placeholder.className = 'drag-placeholder';
  dragState.placeholder.style.height = item.offsetHeight + 'px';

  // 在原位置插入占位符
  item.parentElement.insertBefore(dragState.placeholder, item.nextSibling);
}

// 更新拖拽元素位置
function updateDraggedElementPosition() {
  const deltaY = dragState.currentY - dragState.startY;
  dragState.draggedElement.style.transform = `translateY(${deltaY}px)`;
  dragState.draggedElement.style.zIndex = '1000';
}

// 更新占位符位置
function updatePlaceholderPosition() {
  const containerRect = dragState.container.getBoundingClientRect();
  const mouseY = dragState.currentY;

  // 找到最接近的插入位置
  let insertIndex = 0;
  const items = dragState.items.filter(item => item !== dragState.draggedElement);

  for (let i = 0; i < items.length; i++) {
    const itemRect = items[i].getBoundingClientRect();
    const itemCenterY = itemRect.top + itemRect.height / 2;

    if (mouseY < itemCenterY) {
      insertIndex = i;
      break;
    } else {
      insertIndex = i + 1;
    }
  }

  // 移动占位符到新位置
  const targetItem = items[insertIndex];
  if (targetItem) {
    dragState.container.insertBefore(dragState.placeholder, targetItem);
  } else {
    dragState.container.appendChild(dragState.placeholder);
  }
}

// 计算新的索引位置
function calculateNewIndex() {
  const placeholderIndex = Array.from(dragState.container.children).indexOf(dragState.placeholder);
  if (placeholderIndex === -1) return -1;

  // 如果占位符在拖拽元素之后，需要调整索引
  return placeholderIndex > dragState.draggedIndex ? placeholderIndex - 1 : placeholderIndex;
}

// 更新排序顺序
function updateSortOrder(type, oldIndex, newIndex) {
  if (type === 'group') {
    updateGroupSortOrder(oldIndex, newIndex);
  } else if (type === 'reply') {
    updateReplySortOrder(oldIndex, newIndex);
  }
}

// 更新分组排序顺序
function updateGroupSortOrder(oldIndex, newIndex) {
  // 重新排列内存中的分组数据
  const movedGroup = groups.splice(oldIndex, 1)[0];
  groups.splice(newIndex, 0, movedGroup);

  // 更新所有分组的sortOrder
  groups.forEach((group, index) => {
    group.sortOrder = index;
  });

  // 保存到数据库
  saveGroupsSortOrder().then(() => {
    renderGroups();
  }).catch(error => {
    console.error('保存分组排序失败:', error);
    alert('保存分组排序失败，请重试');
  });
}

// 更新回复排序顺序
function updateReplySortOrder(oldIndex, newIndex) {
  if (!currentGroupId || !replies[currentGroupId]) return;

  // 重新排列内存中的回复数据
  const groupReplies = replies[currentGroupId];
  const movedReply = groupReplies.splice(oldIndex, 1)[0];
  groupReplies.splice(newIndex, 0, movedReply);

  // 更新该分组内所有回复的sortOrder
  groupReplies.forEach((reply, index) => {
    reply.sortOrder = index;
  });

  // 保存到数据库
  saveRepliesSortOrder(currentGroupId).then(() => {
    renderReplies(currentGroupId);
  }).catch(error => {
    console.error('保存回复排序失败:', error);
    alert('保存回复排序失败，请重试');
  });
}

// 保存分组排序到数据库
function saveGroupsSortOrder() {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['groups'], 'readwrite');
    const store = transaction.objectStore('groups');

    let completed = 0;
    const total = groups.length;

    if (total === 0) {
      resolve();
      return;
    }

    groups.forEach(group => {
      const request = store.put(group);
      request.onsuccess = function() {
        completed++;
        if (completed === total) {
          resolve();
        }
      };
      request.onerror = function(event) {
        reject(event.target.error);
      };
    });
  });
}

// 保存回复排序到数据库
function saveRepliesSortOrder(groupId) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['replies'], 'readwrite');
    const store = transaction.objectStore('replies');

    const groupReplies = replies[groupId] || [];
    let completed = 0;
    const total = groupReplies.length;

    if (total === 0) {
      resolve();
      return;
    }

    groupReplies.forEach(reply => {
      const request = store.put(reply);
      request.onsuccess = function() {
        completed++;
        if (completed === total) {
          resolve();
        }
      };
      request.onerror = function(event) {
        reject(event.target.error);
      };
    });
  });
}

// 清理拖拽状态
function cleanupDragState() {
  // 取消待处理的动画帧
  if (dragState.animationFrame) {
    cancelAnimationFrame(dragState.animationFrame);
  }

  if (dragState.draggedElement) {
    dragState.draggedElement.classList.remove('dragging');
    dragState.draggedElement.style.transform = '';
    dragState.draggedElement.style.zIndex = '';
  }

  if (dragState.placeholder && dragState.placeholder.parentElement) {
    dragState.placeholder.parentElement.removeChild(dragState.placeholder);
  }

  document.body.classList.remove('dragging-active');

  // 清理潜在拖拽状态
  cleanupPotentialDrag();

  // 重置拖拽状态
  dragState = {
    isDragging: false,
    draggedElement: null,
    draggedIndex: -1,
    draggedType: null,
    placeholder: null,
    startX: 0,
    startY: 0,
    currentY: 0,
    offsetY: 0,
    container: null,
    items: [],
    animationFrame: null,
    potentialDrag: null
  };
}