// 初始化IndexedDB数据库
function initDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('TristanQuickReplyDB', 2); // 增加版本号以支持数据迁移

    request.onerror = function(event) {
      console.error('数据库打开失败:', event.target.error);
      reject(event.target.error);
    };

    request.onsuccess = function(event) {
      const db = event.target.result;
      console.log('数据库连接成功');
      resolve(db);
    };

    request.onupgradeneeded = function(event) {
      const db = event.target.result;
      const oldVersion = event.oldVersion;

      // 创建存储组和回复数据的对象存储
      if (!db.objectStoreNames.contains('groups')) {
        db.createObjectStore('groups', { keyPath: 'id' });
      }

      if (!db.objectStoreNames.contains('replies')) {
        const repliesStore = db.createObjectStore('replies', { keyPath: 'id', autoIncrement: true });
        repliesStore.createIndex('groupId', 'groupId', { unique: false });
      }

      if (!db.objectStoreNames.contains('settings')) {
        db.createObjectStore('settings', { keyPath: 'key' });
      }

      // 版本2：添加排序字段的数据迁移
      if (oldVersion < 2) {
        console.log('开始数据迁移：添加排序字段');

        // 迁移分组数据，添加sortOrder字段
        const groupsTransaction = event.target.transaction;
        if (groupsTransaction && db.objectStoreNames.contains('groups')) {
          const groupsStore = groupsTransaction.objectStore('groups');
          const groupsRequest = groupsStore.getAll();

          groupsRequest.onsuccess = function() {
            const groups = groupsRequest.result;
            groups.forEach((group, index) => {
              if (group.sortOrder === undefined) {
                group.sortOrder = index;
                groupsStore.put(group);
              }
            });
          };
        }

        // 迁移回复数据，添加sortOrder字段
        if (groupsTransaction && db.objectStoreNames.contains('replies')) {
          const repliesStore = groupsTransaction.objectStore('replies');
          const repliesRequest = repliesStore.getAll();

          repliesRequest.onsuccess = function() {
            const replies = repliesRequest.result;
            // 按groupId分组，然后为每组内的回复分配sortOrder
            const groupedReplies = {};
            replies.forEach(reply => {
              if (!groupedReplies[reply.groupId]) {
                groupedReplies[reply.groupId] = [];
              }
              groupedReplies[reply.groupId].push(reply);
            });

            Object.keys(groupedReplies).forEach(groupId => {
              groupedReplies[groupId].forEach((reply, index) => {
                if (reply.sortOrder === undefined) {
                  reply.sortOrder = index;
                  repliesStore.put(reply);
                }
              });
            });
          };
        }
      }
    };
  });
}

// 检查数据库是否为空
function isDatabaseEmpty(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['groups'], 'readonly');
    const store = transaction.objectStore('groups');
    const countRequest = store.count();
    
    countRequest.onsuccess = function() {
      resolve(countRequest.result === 0);
    };
    
    countRequest.onerror = function(event) {
      console.error('检查数据库失败:', event.target.error);
      reject(event.target.error);
    };
  });
}

// 初始化默认数据
function initializeDefaultData(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['groups'], 'readwrite');
    const store = transaction.objectStore('groups');
    
    // 添加默认分组
    const defaultGroup = { id: '1', name: '默认' };
    const request = store.add(defaultGroup);
    
    request.onsuccess = function() {
      console.log('初始化默认数据成功');
      resolve();
    };
    
    request.onerror = function(event) {
      console.error('初始化默认数据失败:', event.target.error);
      reject(event.target.error);
    };
  });
}

// 扩展安装或更新时的监听器
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    // 首次安装时，初始化数据库和默认数据
    initDatabase().then(db => {
      return isDatabaseEmpty(db).then(isEmpty => {
        if (isEmpty) {
          return initializeDefaultData(db);
        }
      });
    }).catch(error => {
      console.error('初始化过程出错:', error);
    });
  }
}); 