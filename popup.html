<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>WhatsApp QuickReply</title>
  <link rel="stylesheet" href="styles.css">
  <!-- 添加图标库 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>WhatsApp Business</h1>
      <div class="header-actions">
        <button id="import-config-btn" class="config-btn" title="导入配置">导入配置</button>
        <button id="export-config-btn" class="config-btn" title="导出配置">导出配置</button>
      </div>
    </header>
    
    <div class="main-content">
      <div class="replies-section">
        <div class="section-header">
          <h2 id="current-group-name">快捷回复</h2>
          <button id="add-reply-btn" class="action-btn">+</button>
        </div>
        <div id="replies-container" class="replies-container">
          <!-- 快捷回复将在这里动态生成 -->
        </div>
      </div>
      
      <div class="groups-section">
        <div class="section-header">
          <h2>分组</h2>
          <button id="add-group-btn" class="action-btn">+</button>
        </div>
        <div id="groups-container" class="groups-container">
          <!-- 分组将在这里动态生成 -->
        </div>
      </div>
    </div>
    
    <!-- 添加分组的弹窗 -->
    <div id="add-group-modal" class="modal">
      <div id="group-modal-content" class="modal-content">
        <span class="close">&times;</span>
        <h2>添加分组</h2>
        <div class="form-group">
          <label for="group-name">分组名称:</label>
          <input type="text" id="group-name" placeholder="例如: 中国客户">
        </div>
        <button id="save-group-btn" class="btn">保存分组</button>
      </div>
    </div>
    
    <!-- 添加快捷回复的弹窗 -->
    <div id="add-reply-modal" class="modal">
      <div id="reply-modal-content" class="modal-content">
        <span class="close">&times;</span>
        <h2>添加快捷回复</h2>
        <div class="form-group">
          <label for="reply-title">标题:</label>
          <input type="text" id="reply-title" placeholder="输入标题">
        </div>
        <div class="form-group">
          <label for="reply-content">内容:</label>
          <textarea id="reply-content" placeholder="输入回复内容..."></textarea>
        </div>
        <div class="form-group image-upload-group">
          <label for="reply-images">图片 (可选，支持多张):</label>
          <div class="custom-file-upload">
            <input type="file" id="reply-images" accept="image/*" multiple>
            <label for="reply-images" class="file-upload-btn">选择图片文件</label>
            <div id="file-name-display" class="file-name-display">未选择文件</div>
          </div>
          <div id="images-preview" class="images-preview"></div>
        </div>
        <button id="save-reply-btn" class="btn">保存快捷回复</button>
      </div>
    </div>
    
    <!-- 编辑快捷回复的弹窗 -->
    <div id="edit-reply-modal" class="modal">
      <div id="edit-reply-modal-content" class="modal-content">
        <span class="close">&times;</span>
        <h2>编辑快捷回复</h2>
        <div class="form-group">
          <label for="edit-reply-title">标题:</label>
          <input type="text" id="edit-reply-title" placeholder="输入标题">
        </div>
        <div class="form-group">
          <label for="edit-reply-content">内容:</label>
          <textarea id="edit-reply-content" placeholder="输入回复内容..."></textarea>
        </div>
        <div class="form-group image-upload-group">
          <label for="edit-reply-images">图片 (可选，支持多张):</label>
          <div class="custom-file-upload">
            <input type="file" id="edit-reply-images" accept="image/*" multiple>
            <label for="edit-reply-images" class="file-upload-btn">选择图片文件</label>
            <div id="edit-file-name-display" class="file-name-display">未选择文件</div>
          </div>
          <div id="edit-images-preview" class="images-preview"></div>
        </div>
        <button id="update-reply-btn" class="btn">保存快捷回复</button>
      </div>
    </div>
    
    <!-- 上下文菜单 -->
    <div id="context-menu" class="context-menu">
      <div class="context-menu-item" data-action="edit">
        编辑
      </div>
      <div class="context-menu-item" data-action="delete">
        删除
      </div>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html> 