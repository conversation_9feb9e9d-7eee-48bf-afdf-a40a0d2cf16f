// 向WhatsApp发送文本
async function sendTextToWhatsApp(text) {
  try {
    // 定位WhatsApp输入框
    const inputField = await findWhatsAppInputField();
    if (!inputField) {
      const isbusiness = window.location.href.includes('business.facebook.com');
      const platform = isbusiness ? 'Business' : 'WhatsApp';
      throw new Error(`无法找到${platform}输入框，请确保您处于聊天窗口中`);
    }
    
    // 彻底清空输入框内容
    await clearInputField(inputField);
    
    // 使用逐行插入文本的方法处理多行文本
    
    // 逐行插入文本，每行后模拟按下Shift+Enter
    const lines = text.split('\n');
    
    // 获取焦点
    inputField.focus();
    await sleep(100);
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (inputField.tagName.toLowerCase() === 'textarea') {
        // Business textarea
        const currentValue = inputField.value;
        const newValue = currentValue + line + (i < lines.length - 1 ? '\n' : '');
        inputField.value = newValue;

        // 触发输入事件
        const inputEvent = new Event('input', { bubbles: true });
        inputField.dispatchEvent(inputEvent);
      } else {
        // WhatsApp contenteditable
        document.execCommand('insertText', false, line);

        // 如果不是最后一行，则插入换行符
        if (i < lines.length - 1) {
          // 模拟按下Shift+Enter键（WhatsApp中的换行）
          const enterEvent = new KeyboardEvent('keydown', {
            bubbles: true,
            cancelable: true,
            keyCode: 13, // Enter键的keyCode
            which: 13,
            key: 'Enter',
            code: 'Enter',
            shiftKey: true
          });
          inputField.dispatchEvent(enterEvent);
          await sleep(10);
        }
      }
    }
    
    // 检查文本是否成功插入
    await sleep(100);
    if (inputField.textContent.trim().length > 0) {
      return { success: true, message: "文本已输入，请手动按Enter键发送" };
    } else {
      return { success: false, error: "无法在WhatsApp输入框中输入文本，请手动输入" };
    }

  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 监听来自扩展程序的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'sendReply') {
    const reply = message.reply;
    
    // 检查当前页面是否是支持的平台
    const isWhatsApp = window.location.href.includes('web.whatsapp.com');
    const isbusiness = window.location.href.includes('business.facebook.com');

    if (!isWhatsApp && !isbusiness) {
      sendResponse({ success: false, error: '请在指定网页中使用此功能~ WhatsApp,Business' });
      return true;
    }
    
    // 尝试发送回复
    const images = reply.images || (reply.image ? [reply.image] : []); // 兼容旧数据
    if (images && images.length > 0) {
      // 图片+文本：先输入文本，再粘贴图片
      sendReplyWithImagesAndText(reply.content, images)
        .then((result) => sendResponse(result))
        .catch(error => sendResponse({ success: false, error: `处理图片和文本失败: ${error.message}` }));
    } else {
      // 只有文字内容
      sendTextToWhatsApp(reply.content)
        .then((result) => sendResponse(result))
        .catch(error => sendResponse({ success: false, error: `输入文本失败: ${error.message}` }));
    }
    return true; // 保持消息通道开放以进行异步响应
  }
  
  return false;
});

// 发送多图片和文本组合
async function sendReplyWithImagesAndText(text, images) {
  try {
    // 检测当前平台
    const isbusiness = window.location.href.includes('business.facebook.com');

    // 定位输入框
    const inputField = await findWhatsAppInputField();
    if (!inputField) {
      const platform = isbusiness ? 'Business' : 'WhatsApp';
      throw new Error(`无法找到${platform}输入框，请确保您处于聊天窗口中`);
    }

    // 彻底清空输入框内容
    await clearInputField(inputField);

    // 只有当文本不为空时才输入文本
    if (text && text.trim()) {
      // 逐行插入文本，每行后模拟按下Shift+Enter
      const lines = text.split('\n');

      // 获取焦点
      inputField.focus();
      await sleep(100);

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (inputField.tagName.toLowerCase() === 'textarea') {
          // Business textarea
          const currentValue = inputField.value;
          const newValue = currentValue + line + (i < lines.length - 1 ? '\n' : '');
          inputField.value = newValue;

          // 触发输入事件
          const inputEvent = new Event('input', { bubbles: true });
          inputField.dispatchEvent(inputEvent);
        } else {
          // WhatsApp contenteditable
          document.execCommand('insertText', false, line);

          // 如果不是最后一行，则插入换行符
          if (i < lines.length - 1) {
            // 模拟按下Shift+Enter键（WhatsApp中的换行）
            const enterEvent = new KeyboardEvent('keydown', {
              bubbles: true,
              cancelable: true,
              keyCode: 13, // Enter键的keyCode
              which: 13,
              key: 'Enter',
              code: 'Enter',
              shiftKey: true
            });
            inputField.dispatchEvent(enterEvent);
            await sleep(10);
          }
        }
      }

      // 检查文本是否成功插入
      await sleep(100);
    }

    // 步骤2: 发送图片
    if (isbusiness) {
      // Business 图片发送 - 使用验证成功的批量发送方案
      try {
        await sendAllImagesAtOnce(inputField, images);
        return { success: true, message: `文本和 ${images.length} 张图片已添加到Business输入框，请手动按Enter键发送` };
      } catch (error) {
        return { success: true, message: `文本已添加到输入框。图片发送失败，请手动上传 ${images.length} 张图片` };
      }
    } else {
      // WhatsApp 图片发送 - 使用验证成功的批量发送方案
      try {
        await sendAllImagesAtOnce(inputField, images);
        return { success: true, message: `文本和 ${images.length} 张图片已一次性添加到输入框，请手动按Enter键发送` };
      } catch (error) {
        return { success: true, message: `文本已添加到输入框。图片发送失败，请手动上传 ${images.length} 张图片` };
      }
    }

  } catch (error) {
    throw error;
  }
}

// 一次性发送所有图片
async function sendAllImagesAtOnce(inputField, images) {
  if (!images || images.length === 0) {
    return { success: true, message: "没有图片需要发送" };
  }

  try {
    // 创建包含所有图片的 DataTransfer 对象
    const clipboardData = new DataTransfer();
    const imagePromises = [];

    // 并行处理所有图片
    for (let i = 0; i < images.length; i++) {
      const base64Image = images[i];

      // 确保base64Image是完整的Data URL
      let imageUrl = base64Image;
      if (!imageUrl.startsWith('data:')) {
        imageUrl = `data:image/png;base64,${base64Image}`;
      }

      const imagePromise = new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          // 创建canvas并绘制图片
          const canvas = document.createElement('canvas');
          canvas.width = img.width || 200;
          canvas.height = img.height || 200;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0);

          canvas.toBlob((blob) => {
            if (blob) {
              // 为每张图片创建唯一的文件名
              const fileName = `image_${i + 1}.png`;
              const file = new File([blob], fileName, { type: 'image/png' });
              clipboardData.items.add(file);
              resolve();
            } else {
              reject(new Error(`无法创建图片 ${i + 1} 的blob`));
            }
          }, 'image/png');
        };

        img.onerror = () => reject(new Error(`图片 ${i + 1} 加载失败`));
        img.src = imageUrl;
      });

      imagePromises.push(imagePromise);
    }

    // 等待所有图片处理完成
    await Promise.all(imagePromises);

    // 获取焦点
    inputField.focus();
    await sleep(100);

    // 创建并分发粘贴事件
    const pasteEvent = new ClipboardEvent('paste', {
      bubbles: true,
      cancelable: true,
      clipboardData: clipboardData
    });

    inputField.dispatchEvent(pasteEvent);

    // 稍作延迟后返回成功
    await sleep(500);
    return { success: true, message: `已一次性添加 ${images.length} 张图片到输入框` };

  } catch (error) {
    throw error;
  }
}

// 发送单张图片
async function sendSingleImage(inputField, base64Image) {
  // 确保base64Image是完整的Data URL
  let imageUrl = base64Image;
  if (!imageUrl.startsWith('data:')) {
    imageUrl = `data:image/png;base64,${base64Image}`;
  }

  // 创建图片元素并获取其数据
  const img = new Image();
  img.src = imageUrl;
  await new Promise((resolve) => {
    img.onload = resolve;
    img.onerror = resolve; // 即使加载失败也继续
    setTimeout(resolve, 1000); // 超时保障
  });

  // 创建canvas并绘制图片
  const canvas = document.createElement('canvas');
  canvas.width = img.width || 200;
  canvas.height = img.height || 200;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(img, 0, 0);

  // 获取焦点（再次确认）
  inputField.focus();

  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (!blob) {
        reject(new Error('无法创建图片blob'));
        return;
      }

      try {
        // 统一处理方式 - Business 和 WhatsApp 都使用相同的粘贴事件
        const clipboardData = new DataTransfer();
        clipboardData.items.add(new File([blob], 'image.png', { type: 'image/png' }));

        const pasteEvent = new ClipboardEvent('paste', {
          bubbles: true,
          cancelable: true,
          clipboardData: clipboardData
        });

        inputField.dispatchEvent(pasteEvent);

        // 稍作延迟后返回成功
        setTimeout(() => {
          resolve({ success: true });
        }, 200);

      } catch (error) {
        // 即使出现错误，依然返回成功，因为实际效果可能已经成功
        resolve({ success: true, message: "可能已成功添加图片，请检查输入框" });
      }
    }, 'image/png');
  });
}





// 查找输入框（支持WhatsApp和Business）
async function findWhatsAppInputField() {
  // 检测当前平台
  const isWhatsApp = window.location.href.includes('web.whatsapp.com');
  const isbusiness = window.location.href.includes('business.facebook.com');

  if (isWhatsApp) {
    // WhatsApp输入框XPath
    const whatsappInputXPath = '//*[@id="main"]/footer/div[1]/div/span/div/div[2]/div/div/div[1]/p';
    let inputField = await waitForElement(whatsappInputXPath, 2000);

    if (inputField) {
      return inputField;
    }

    console.error("WhatsApp输入框定位失败: 无法通过XPath找到元素", whatsappInputXPath);
    return null;
  } else if (isbusiness) {
    // Business输入框 - 使用成功验证的CSS选择器
    //const cssSelector = 'textarea[placeholder*="通过 Messenger 回复"]';
    const cssSelector = 'textarea[placeholder*="通过"][placeholder*="回复"]';

    try {
      const elements = document.querySelectorAll(cssSelector);
      for (const element of elements) {
        if (element.offsetParent !== null && element.offsetWidth > 0 && element.offsetHeight > 0) {
          return element;
        }
      }
    } catch (error) {
      console.warn("CSS选择器失败:", cssSelector, error);
    }

    console.error("Business输入框定位失败: 无法找到可用元素");
    return null;
  }

  console.error("不支持的平台");
  return null;
}

// 辅助函数：等待元素出现且可交互
async function waitForElement(xpath, timeout = 5000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    const element = getElementByXPath(xpath);
    if (element) {
      // 检查元素是否有效且可见
      const isVisible = element.offsetParent !== null && 
                        !!(element.offsetWidth || element.offsetHeight || element.getClientRects().length);
      
      if (isVisible) {
        // 额外等待一小段时间确保元素完全加载并可交互
        await sleep(50);
        return element;
      }
    }
    
    await sleep(100);
  }
  
  return null;
}

// 辅助函数：通过XPath获取元素
function getElementByXPath(xpath) {
  return document.evaluate(
    xpath,
    document,
    null,
    XPathResult.FIRST_ORDERED_NODE_TYPE,
    null
  ).singleNodeValue;
}

// 辅助函数：等待指定毫秒数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 彻底清空输入框内容的函数 - 支持WhatsApp和Business
async function clearInputField(inputField) {

  // 获取焦点
  inputField.focus();
  await sleep(100);

  // 检测输入框类型
  if (inputField.tagName.toLowerCase() === 'textarea') {
    // Business textarea
    inputField.value = '';

    // 触发输入事件
    const inputEvent = new Event('input', { bubbles: true });
    inputField.dispatchEvent(inputEvent);

    const changeEvent = new Event('change', { bubbles: true });
    inputField.dispatchEvent(changeEvent);

    return true;
  } else {
    // WhatsApp contenteditable - 用于检查是否清空的辅助函数
    const isFieldEmpty = () => {
      const hasText = inputField.textContent.trim().length > 0;
      const hasHTML = inputField.innerHTML.replace(/<br>|<div><br><\/div>/gi, '').trim().length > 0;
      return !hasText && !hasHTML;
    };

    // 使用keyboard事件模拟Ctrl+A和Delete
    try {
      // 模拟Ctrl+A (全选)
      const selectAllEvent = new KeyboardEvent('keydown', {
        bubbles: true,
        cancelable: true,
        keyCode: 65, // 'A'键的keyCode
        which: 65,
        key: 'a',
        code: 'KeyA',
        ctrlKey: true
      });
      inputField.dispatchEvent(selectAllEvent);
      await sleep(50);

      // 模拟Delete按键
      const deleteEvent = new KeyboardEvent('keydown', {
        bubbles: true,
        cancelable: true,
        keyCode: 46, // Delete键的keyCode
        which: 46,
        key: 'Delete',
        code: 'Delete'
      });
      inputField.dispatchEvent(deleteEvent);
      await sleep(50);

      // 检查是否清空成功
      if (isFieldEmpty()) {
        return true;
      }
    } catch (e) {
      // 键盘事件模拟清空失败
    }

    // 如果方法1失败，返回true继续处理
    return true;
  }
}